'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, Github, Folder, Play, Bug, Package, Zap, Brain, Code2, FileText, Settings } from 'lucide-react';
import { motion } from 'framer-motion';
import toast, { Toaster } from 'react-hot-toast';

interface ProjectFile {
  name: string;
  content: string;
  path: string;
  type: 'python' | 'text' | 'other';
}

interface ProjectAnalysis {
  files: ProjectFile[];
  dependencies: string[];
  functions: string[];
  classes: string[];
  errors: string[];
  suggestions: string[];
}

export default function Home() {
  const [project, setProject] = useState<ProjectAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('upload');
  const [githubUrl, setGithubUrl] = useState('');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setLoading(true);
    toast.loading('جاري تحليل الملفات...');

    // محاكاة تحليل الملفات
    setTimeout(() => {
      const files: ProjectFile[] = acceptedFiles.map(file => ({
        name: file.name,
        content: 'محتوى الملف سيتم قراءته هنا...',
        path: file.name,
        type: file.name.endsWith('.py') ? 'python' : 'other'
      }));

      const analysis: ProjectAnalysis = {
        files,
        dependencies: ['numpy', 'pandas', 'requests', 'flask'],
        functions: ['main()', 'process_data()', 'api_call()'],
        classes: ['DataProcessor', 'APIClient'],
        errors: [],
        suggestions: ['إضافة معالجة الأخطاء', 'تحسين الأداء', 'إضافة التوثيق']
      };

      setProject(analysis);
      setLoading(false);
      toast.dismiss();
      toast.success('تم تحليل المشروع بنجاح!');
    }, 2000);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/x-python': ['.py'],
      'text/plain': ['.txt', '.md', '.rst'],
      'application/json': ['.json'],
      'text/yaml': ['.yml', '.yaml']
    }
  });

  const handleGithubImport = async () => {
    if (!githubUrl) {
      toast.error('يرجى إدخال رابط GitHub');
      return;
    }

    setLoading(true);
    toast.loading('جاري تحميل المشروع من GitHub...');

    // محاكاة تحميل من GitHub
    setTimeout(() => {
      const mockProject: ProjectAnalysis = {
        files: [
          { name: 'main.py', content: 'print("Hello World")', path: 'main.py', type: 'python' },
          { name: 'requirements.txt', content: 'flask\nrequests', path: 'requirements.txt', type: 'text' }
        ],
        dependencies: ['flask', 'requests'],
        functions: ['main()', 'setup()'],
        classes: ['App'],
        errors: [],
        suggestions: ['إضافة اختبارات', 'تحسين البنية']
      };

      setProject(mockProject);
      setLoading(false);
      toast.dismiss();
      toast.success('تم تحميل المشروع من GitHub بنجاح!');
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Toaster position="top-right" />

      {/* Header */}
      <header className="bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg">
                <Code2 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">PyDev Studio Pro</h1>
                <p className="text-sm text-gray-300">أقوى منصة لتطوير Python بالذكاء الاصطناعي</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button className="bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors">
                <Settings className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {!project ? (
          <div className="max-w-4xl mx-auto">
            {/* Navigation Tabs */}
            <div className="flex space-x-1 bg-white/5 p-1 rounded-lg mb-8">
              <button
                onClick={() => setActiveTab('upload')}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-colors ${
                  activeTab === 'upload' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'
                }`}
              >
                <Upload className="h-5 w-5" />
                <span>تحميل ملفات</span>
              </button>
              <button
                onClick={() => setActiveTab('github')}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-colors ${
                  activeTab === 'github' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'
                }`}
              >
                <Github className="h-5 w-5" />
                <span>من GitHub</span>
              </button>
              <button
                onClick={() => setActiveTab('folder')}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-colors ${
                  activeTab === 'folder' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'
                }`}
              >
                <Folder className="h-5 w-5" />
                <span>مجلد محلي</span>
              </button>
            </div>

            {/* Upload Area */}
            {activeTab === 'upload' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-8"
              >
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-xl p-12 text-center transition-colors cursor-pointer ${
                    isDragActive ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600 hover:border-gray-500'
                  }`}
                >
                  <input {...getInputProps()} />
                  <Upload className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {isDragActive ? 'اسحب الملفات هنا...' : 'اسحب ملفات Python أو انقر للاختيار'}
                  </h3>
                  <p className="text-gray-400">
                    يدعم ملفات .py, .txt, .md, .json, .yml
                  </p>
                </div>
              </motion.div>
            )}

            {/* GitHub Import */}
            {activeTab === 'github' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-8"
              >
                <div className="text-center mb-6">
                  <Github className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">استيراد من GitHub</h3>
                  <p className="text-gray-400">أدخل رابط المستودع لتحميله وتحليله</p>
                </div>

                <div className="space-y-4">
                  <input
                    type="url"
                    value={githubUrl}
                    onChange={(e) => setGithubUrl(e.target.value)}
                    placeholder="https://github.com/username/repository"
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <button
                    onClick={handleGithubImport}
                    disabled={loading}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors disabled:opacity-50"
                  >
                    {loading ? 'جاري التحميل...' : 'تحميل المشروع'}
                  </button>
                </div>
              </motion.div>
            )}

            {/* Local Folder */}
            {activeTab === 'folder' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-8"
              >
                <div className="text-center">
                  <Folder className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">اختيار مجلد محلي</h3>
                  <p className="text-gray-400 mb-6">اختر مجلد المشروع من جهازك</p>
                  <button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                    اختيار مجلد
                  </button>
                </div>
              </motion.div>
            )}
          </div>
        ) : (
          /* Project Dashboard */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Project Overview */}
              <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6">
                <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <FileText className="h-6 w-6 mr-2" />
                  ملفات المشروع ({project.files.length})
                </h2>
                <div className="space-y-2">
                  {project.files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-white/5 rounded-lg p-3">
                      <div className="flex items-center">
                        <Code2 className="h-5 w-5 text-blue-400 mr-2" />
                        <span className="text-white">{file.name}</span>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs ${
                        file.type === 'python' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                      }`}>
                        {file.type}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* AI Suggestions */}
              <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6">
                <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <Brain className="h-6 w-6 mr-2" />
                  اقتراحات الذكاء الاصطناعي
                </h2>
                <div className="space-y-3">
                  {project.suggestions.map((suggestion, index) => (
                    <div key={index} className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                      <p className="text-blue-300">{suggestion}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6">
                <h3 className="text-lg font-semibold text-white mb-4">إجراءات سريعة</h3>
                <div className="space-y-3">
                  <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center">
                    <Play className="h-4 w-4 mr-2" />
                    تشغيل
                  </button>
                  <button className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center">
                    <Bug className="h-4 w-4 mr-2" />
                    فحص الأخطاء
                  </button>
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center">
                    <Package className="h-4 w-4 mr-2" />
                    إدارة المكتبات
                  </button>
                  <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center">
                    <Zap className="h-4 w-4 mr-2" />
                    تحويل لتنفيذي
                  </button>
                </div>
              </div>

              {/* Dependencies */}
              <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6">
                <h3 className="text-lg font-semibold text-white mb-4">المكتبات المطلوبة</h3>
                <div className="space-y-2">
                  {project.dependencies.map((dep, index) => (
                    <div key={index} className="flex items-center justify-between bg-white/5 rounded-lg p-2">
                      <span className="text-white text-sm">{dep}</span>
                      <span className="text-green-400 text-xs">مثبت</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Project Stats */}
              <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6">
                <h3 className="text-lg font-semibold text-white mb-4">إحصائيات المشروع</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">الملفات</span>
                    <span className="text-white">{project.files.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">الدوال</span>
                    <span className="text-white">{project.functions.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">الكلاسات</span>
                    <span className="text-white">{project.classes.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">المكتبات</span>
                    <span className="text-white">{project.dependencies.length}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
