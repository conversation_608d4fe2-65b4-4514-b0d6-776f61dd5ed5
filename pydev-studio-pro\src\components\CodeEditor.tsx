'use client';

import { useState, useRef } from 'react';
import Editor from '@monaco-editor/react';
import { Play, Save, Download, Copy, Maximize2, Minimize2 } from 'lucide-react';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';

interface CodeEditorProps {
  file: {
    name: string;
    content: string;
    language: string;
  };
  onSave?: (content: string) => void;
  onRun?: (content: string) => void;
}

export default function CodeEditor({ file, onSave, onRun }: CodeEditorProps) {
  const [code, setCode] = useState(file.content);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [theme, setTheme] = useState('vs-dark');
  const editorRef = useRef<any>(null);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;
    
    // إعداد الاختصارات
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });
    
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      handleRun();
    });
  };

  const handleSave = () => {
    if (onSave) {
      onSave(code);
      toast.success('تم حفظ الملف بنجاح');
    }
  };

  const handleRun = () => {
    if (onRun) {
      onRun(code);
      toast.success('جاري تشغيل الكود...');
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    toast.success('تم نسخ الكود');
  };

  const handleDownload = () => {
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('تم تحميل الملف');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden ${
        isFullscreen ? 'fixed inset-4 z-50' : ''
      }`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
          <span className="text-white font-medium">{file.name}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleCopy}
            className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            title="نسخ الكود"
          >
            <Copy className="h-4 w-4" />
          </button>
          <button
            onClick={handleDownload}
            className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            title="تحميل الملف"
          >
            <Download className="h-4 w-4" />
          </button>
          <button
            onClick={handleSave}
            className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            title="حفظ (Ctrl+S)"
          >
            <Save className="h-4 w-4" />
          </button>
          <button
            onClick={handleRun}
            className="p-2 text-green-400 hover:text-green-300 hover:bg-green-500/10 rounded-lg transition-colors"
            title="تشغيل (Ctrl+Enter)"
          >
            <Play className="h-4 w-4" />
          </button>
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            title={isFullscreen ? 'تصغير' : 'ملء الشاشة'}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </button>
        </div>
      </div>

      {/* Editor */}
      <div className={`${isFullscreen ? 'h-[calc(100vh-8rem)]' : 'h-96'}`}>
        <Editor
          height="100%"
          defaultLanguage={file.language}
          value={code}
          onChange={(value) => setCode(value || '')}
          onMount={handleEditorDidMount}
          theme={theme}
          options={{
            fontSize: 14,
            fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 4,
            insertSpaces: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            renderWhitespace: 'selection',
            bracketPairColorization: { enabled: true },
            suggest: {
              showKeywords: true,
              showSnippets: true,
              showFunctions: true,
              showVariables: true,
            },
          }}
        />
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between p-2 text-xs text-gray-400 border-t border-white/10">
        <div className="flex items-center space-x-4">
          <span>اللغة: {file.language}</span>
          <span>الأسطر: {code.split('\n').length}</span>
          <span>الأحرف: {code.length}</span>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={theme}
            onChange={(e) => setTheme(e.target.value)}
            className="bg-white/10 border border-white/20 rounded px-2 py-1 text-white text-xs"
          >
            <option value="vs-dark">Dark</option>
            <option value="light">Light</option>
            <option value="hc-black">High Contrast</option>
          </select>
        </div>
      </div>
    </motion.div>
  );
}
