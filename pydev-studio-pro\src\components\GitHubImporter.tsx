'use client';

import { useState } from 'react';
import { Github, Download, Folder, File, Star, GitBranch } from 'lucide-react';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';

interface GitHubRepo {
  name: string;
  description: string;
  stars: number;
  language: string;
  url: string;
  files: GitHubFile[];
}

interface GitHubFile {
  name: string;
  path: string;
  type: 'file' | 'dir';
  content?: string;
  size: number;
}

interface GitHubImporterProps {
  onImport: (repo: GitHubRepo) => void;
}

export default function GitHubImporter({ onImport }: GitHubImporterProps) {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [repo, setRepo] = useState<GitHubRepo | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  const parseGitHubUrl = (url: string) => {
    const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (match) {
      return { owner: match[1], repo: match[2] };
    }
    return null;
  };

  const fetchRepoInfo = async () => {
    if (!url) {
      toast.error('يرجى إدخال رابط GitHub صحيح');
      return;
    }

    const parsed = parseGitHubUrl(url);
    if (!parsed) {
      toast.error('رابط GitHub غير صحيح');
      return;
    }

    setLoading(true);
    toast.loading('جاري جلب معلومات المستودع...');

    try {
      // محاكاة استدعاء GitHub API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockRepo: GitHubRepo = {
        name: parsed.repo,
        description: 'مشروع Python رائع مع ميزات متقدمة',
        stars: 1250,
        language: 'Python',
        url: url,
        files: [
          { name: 'main.py', path: 'main.py', type: 'file', size: 1024, content: 'print("Hello World")' },
          { name: 'requirements.txt', path: 'requirements.txt', type: 'file', size: 256, content: 'flask\nrequests\nnumpy' },
          { name: 'src', path: 'src', type: 'dir', size: 0 },
          { name: 'utils.py', path: 'src/utils.py', type: 'file', size: 2048, content: 'def helper_function():\n    pass' },
          { name: 'config.py', path: 'config.py', type: 'file', size: 512, content: 'DEBUG = True\nAPI_KEY = "your-key"' },
          { name: 'README.md', path: 'README.md', type: 'file', size: 1536, content: '# My Python Project\n\nThis is awesome!' }
        ]
      };

      setRepo(mockRepo);
      setSelectedFiles(mockRepo.files.filter(f => f.type === 'file' && f.name.endsWith('.py')).map(f => f.path));
      toast.dismiss();
      toast.success('تم جلب المستودع بنجاح!');
    } catch (error) {
      toast.dismiss();
      toast.error('فشل في جلب المستودع');
    } finally {
      setLoading(false);
    }
  };

  const handleFileToggle = (filePath: string) => {
    setSelectedFiles(prev => 
      prev.includes(filePath) 
        ? prev.filter(p => p !== filePath)
        : [...prev, filePath]
    );
  };

  const handleImport = () => {
    if (!repo) return;
    
    const filteredRepo = {
      ...repo,
      files: repo.files.filter(f => selectedFiles.includes(f.path))
    };
    
    onImport(filteredRepo);
    toast.success('تم استيراد المشروع بنجاح!');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* URL Input */}
      <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6">
        <div className="flex items-center mb-4">
          <Github className="h-6 w-6 text-white mr-2" />
          <h3 className="text-lg font-semibold text-white">استيراد من GitHub</h3>
        </div>
        
        <div className="space-y-4">
          <input
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://github.com/username/repository"
            className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyPress={(e) => e.key === 'Enter' && fetchRepoInfo()}
          />
          <button
            onClick={fetchRepoInfo}
            disabled={loading}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? 'جاري الجلب...' : 'جلب المستودع'}
          </button>
        </div>
      </div>

      {/* Repository Info */}
      {repo && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-xl font-semibold text-white">{repo.name}</h3>
              <p className="text-gray-400">{repo.description}</p>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <div className="flex items-center">
                <Star className="h-4 w-4 mr-1" />
                {repo.stars}
              </div>
              <div className="flex items-center">
                <GitBranch className="h-4 w-4 mr-1" />
                {repo.language}
              </div>
            </div>
          </div>

          {/* File List */}
          <div className="space-y-2 mb-6">
            <h4 className="text-lg font-medium text-white mb-3">الملفات ({repo.files.length})</h4>
            <div className="max-h-64 overflow-y-auto space-y-1">
              {repo.files.map((file, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-3 rounded-lg transition-colors ${
                    file.type === 'file' ? 'hover:bg-white/5 cursor-pointer' : 'bg-white/5'
                  }`}
                  onClick={() => file.type === 'file' && handleFileToggle(file.path)}
                >
                  <div className="flex items-center">
                    {file.type === 'file' && (
                      <input
                        type="checkbox"
                        checked={selectedFiles.includes(file.path)}
                        onChange={() => handleFileToggle(file.path)}
                        className="mr-3 rounded border-gray-600 bg-white/10 text-blue-600 focus:ring-blue-500"
                      />
                    )}
                    {file.type === 'dir' ? (
                      <Folder className="h-5 w-5 text-blue-400 mr-2" />
                    ) : (
                      <File className="h-5 w-5 text-gray-400 mr-2" />
                    )}
                    <span className="text-white">{file.name}</span>
                  </div>
                  {file.type === 'file' && (
                    <span className="text-xs text-gray-400">{formatFileSize(file.size)}</span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Import Button */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-400">
              تم اختيار {selectedFiles.length} ملف
            </span>
            <button
              onClick={handleImport}
              disabled={selectedFiles.length === 0}
              className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors disabled:opacity-50 flex items-center"
            >
              <Download className="h-4 w-4 mr-2" />
              استيراد المشروع
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
}
